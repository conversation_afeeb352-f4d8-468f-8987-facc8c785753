package com.lfb.android.footprint.ui.components.mapScreen

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.mapbox.geojson.Feature
import com.mapbox.geojson.FeatureCollection
import com.mapbox.geojson.LineString
import com.mapbox.geojson.Point
import com.mapbox.maps.CameraOptions
import com.mapbox.maps.EdgeInsets
import com.mapbox.maps.Style
import com.mapbox.maps.extension.compose.MapboxMap
import com.mapbox.maps.extension.compose.MapEffect
import com.mapbox.maps.extension.style.layers.generated.circleLayer
import com.mapbox.maps.extension.style.layers.generated.lineLayer
import com.mapbox.maps.extension.style.layers.generated.symbolLayer
import com.mapbox.maps.extension.style.sources.generated.geoJsonSource
import com.lfb.android.footprint.model.StepDataRealmModel
import com.lfb.android.footprint.ui.components.fitTrackAndAnimate
import com.lfb.android.footprint.ui.components.fitTrackAndAnimateWithPadding
import com.lfb.android.footprint.prefs.AppPrefs
import com.mapbox.android.gestures.MoveGestureDetector
import com.mapbox.maps.extension.compose.animation.viewport.MapViewportState
import com.mapbox.maps.extension.style.expressions.dsl.generated.literal
import com.mapbox.maps.extension.style.layers.addLayer
import com.mapbox.maps.extension.style.sources.addSource
import com.mapbox.maps.extension.style.sources.generated.GeoJsonSource
import com.mapbox.maps.extension.style.sources.getSource
import com.mapbox.maps.plugin.animation.MapAnimationOptions
import com.mapbox.maps.plugin.animation.easeTo
import com.mapbox.maps.plugin.gestures.OnMoveListener
import com.mapbox.maps.plugin.gestures.addOnMoveListener
import com.mapbox.maps.plugin.locationcomponent.LocationComponentPlugin
import com.mapbox.maps.plugin.locationcomponent.location
import com.mapbox.maps.toCameraOptions
import java.text.SimpleDateFormat
import java.util.*

/**
 * 颜色转换工具函数 - 将十六进制字符串转换为地图可用的颜色字符串
 */
fun hexStringToMapColor(hexString: String): String {
    return try {
        val cleanHex = hexString.removePrefix("0x").removePrefix("#")
        // 如果是8位ARGB格式，取后6位RGB部分
        val rgbHex = if (cleanHex.length == 8) {
            cleanHex.substring(2) // 去掉前两位Alpha通道
        } else if (cleanHex.length == 6) {
            cleanHex
        } else {
            "FF0000" // 默认红色
        }
        val result = "#$rgbHex"
        println("MapContent: Color $hexString -> $result")
        result
    } catch (e: Exception) {
        println("MapContent: Color conversion error: ${e.message}")
        "#FF0000" // 默认红色
    }
}

@Composable
fun MapContent(
    mapViewportState: MapViewportState,
    trackPoints: MutableList<Point>,
    trackDrawVersion: Int,
    multiDayTrackPoints: Map<String, List<Point>> = emptyMap(),
    multiDayTrackDrawVersion: Int = 0,
    multiDayTrackSegments: Map<String, List<List<Point>>> = emptyMap(),
    detailPanelTrackPoints: List<Point>,
    detailTrackDrawVersion: Int,
    selectedDataPoint: Point?,
    selectedDataPointInfo: StepDataRealmModel?,
    showDetailPanel: Boolean,
    drawPointAnimationType: DrawPointAnimationType,
    detailPanelHeight: Int,
    screenHeight: Double,
    // 一生模式相关参数
    selectedFilter: Int = 2,
    lifetimeDataPoints: List<Point> = emptyList(),
    lifetimeDataVersion: Int = 0,
    // 原始轨迹相关参数
    rawTrackPoints: List<Point> = emptyList(),
    rawTrackDrawVersion: Int = 0,
    showRawTrack: Boolean = false,
    // 地图配置参数
    mapDisplayType: Int = 0,
    mapShowAddressName: Boolean = false,
    mapDrawLineAlpha: Double = 0.8,
    mapDrawSpotAlpha: Double = 0.8,
    mapDrawLineWidth: Int = 2,
    mapDrawLineColor: String = "0xFFFF0000",
    onViewportChanged: (Double, Double, Double, Double, Int, Int, Double) -> Unit = { _, _, _, _, _, _, _ -> },
    onMapStyleLoaded: () -> Unit,
    onDrawPointAnimationTypeChanged: (DrawPointAnimationType) -> Unit
) {
    MapboxMap(
        modifier = Modifier.fillMaxSize(),
        mapViewportState = mapViewportState,
        attribution = { },
        logo = { },
        scaleBar = { },
        compass = { }
    ) {
        // 位置设置
        MapEffect() { mapView ->
            mapView.location.updateSettings {
                puckBearingEnabled = true
                enabled = true
                pulsingMaxRadius = 50f
            }
        }

        // 缩放动画处理
        MapEffect(drawPointAnimationType) { mapView ->
            if (drawPointAnimationType == DrawPointAnimationType.SCALE_MODE) {
                // 自动调整视角
                fitTrackAndAnimateWithPadding(
                    mapView = mapView,
                    mapViewportState = mapViewportState,
                    trackPoints = trackPoints,
                )
            }
        }

        // 主轨迹绘制 - 添加地图样式配置和多日轨迹作为依赖项
        MapEffect(trackPoints, trackDrawVersion, multiDayTrackPoints, multiDayTrackDrawVersion, multiDayTrackSegments, detailPanelTrackPoints, showDetailPanel, selectedFilter,
                  rawTrackPoints, rawTrackDrawVersion, showRawTrack, mapDisplayType, mapShowAddressName, mapDrawLineAlpha, mapDrawLineWidth, mapDrawLineColor) { mapView ->

            mapView.getMapboxMap().loadStyleUri(getMapStyleUrl(mapDisplayType, mapShowAddressName)) {
                style -> onMapStyleLoaded()

                // 如果是一生模式，在地图样式加载完成后触发初始视口变化以加载数据
                if (selectedFilter == 0) {
                    println("lifetime: Map style loaded, triggering initial viewport data loading")

                    // 获取当前地图边界并触发数据加载
                    val bounds = mapView.getMapboxMap().coordinateBoundsForCamera(
                        mapView.getMapboxMap().cameraState.toCameraOptions()
                    )
                    val zoomLevel = mapView.getMapboxMap().cameraState.zoom
                    val screenWidth = mapView.width
                    val screenHeight = mapView.height

                    println("lifetime: Initial viewport bounds - lat: [${bounds.southwest.latitude()}, ${bounds.northeast.latitude()}], lng: [${bounds.southwest.longitude()}, ${bounds.northeast.longitude()}], zoom: $zoomLevel, screen: ${screenWidth}x${screenHeight}")

                    if (screenWidth > 0 && screenHeight > 0) {
                        onViewportChanged(
                            bounds.southwest.latitude(),
                            bounds.northeast.latitude(),
                            bounds.southwest.longitude(),
                            bounds.northeast.longitude(),
                            screenWidth,
                            screenHeight,
                            zoomLevel
                        )
                        println("lifetime: Initial viewport change triggered")
                    } else {
                        println("lifetime: Skipping initial viewport change - invalid screen dimensions")
                    }
                }

                style.removeStyleLayer("line-layer")
                style.removeStyleSource("line-source")

                // 只有在详细面板未显示且不是一生模式且是单日模式时才显示主轨迹
                if (trackPoints.isNotEmpty() && !showDetailPanel && selectedFilter != 0 && selectedFilter != 1 && selectedFilter != 4) {
                    style.addSource(geoJsonSource("line-source") {
                        feature(Feature.fromGeometry(LineString.fromLngLats(trackPoints)))
                    })
                    style.addLayer(
                        lineLayer("line-layer", "line-source") {
                            lineColor(hexStringToMapColor(mapDrawLineColor))
                            lineWidth(mapDrawLineWidth.toDouble())
                            lineOpacity(mapDrawLineAlpha)
                        }
                    )

                    if (drawPointAnimationType == DrawPointAnimationType.SCALE_MODE) {
                        fitTrackAndAnimateWithPadding(
                            mapView = mapView,
                            mapViewportState = mapViewportState,
                            trackPoints = trackPoints,
                        )
                    }
                }

                // 清除现有的多日轨迹图层（包括旧的按天图层和新的分段图层）
                // 清除旧的按天图层
                multiDayTrackPoints.keys.forEachIndexed { index, _ ->
                    try {
                        style.removeStyleLayer("multi-day-line-layer-$index")
                        style.removeStyleSource("multi-day-line-source-$index")
                    } catch (e: Exception) {
                        // 图层不存在时会抛出异常，忽略
                    }
                }

                // 清除分段图层（可能有很多分段，所以清理更多的图层）
                for (i in 0..999) { // 清理足够多的图层索引
                    try {
                        style.removeStyleLayer("multi-day-segment-layer-$i")
                        style.removeStyleSource("multi-day-segment-source-$i")
                    } catch (e: Exception) {
                        // 图层不存在时会抛出异常，忽略
                    }
                }

                // 只有在详细面板未显示且是多日模式时才显示多日轨迹（使用距离过滤后的分段数据）
                if (multiDayTrackSegments.isNotEmpty() && !showDetailPanel && (selectedFilter == 1 || selectedFilter == 4)) {
                    val allTrackPoints = mutableListOf<Point>()
                    var layerIndex = 0

                    multiDayTrackSegments.entries.forEach { (dayKey, daySegments) ->
                        daySegments.forEach { segment ->
                            if (segment.isNotEmpty()) {
                                // 为每个分段创建独立的轨迹图层
                                style.addSource(geoJsonSource("multi-day-segment-source-$layerIndex") {
                                    feature(Feature.fromGeometry(LineString.fromLngLats(segment)))
                                })
                                style.addLayer(
                                    lineLayer("multi-day-segment-layer-$layerIndex", "multi-day-segment-source-$layerIndex") {
                                        lineColor(hexStringToMapColor(mapDrawLineColor))
                                        lineWidth(mapDrawLineWidth.toDouble())
                                        lineOpacity(mapDrawLineAlpha)
                                    }
                                )

                                // 收集所有轨迹点用于自动缩放
                                allTrackPoints.addAll(segment)
                                layerIndex++
                            }
                        }
                    }

                    if (drawPointAnimationType == DrawPointAnimationType.SCALE_MODE && allTrackPoints.isNotEmpty()) {
                        // 自动调整视角
                        fitTrackAndAnimateWithPadding(
                            mapView = mapView,
                            mapViewportState = mapViewportState,
                            trackPoints = allTrackPoints,
                        )

                    }
                }

                // 绘制原始轨迹（未过滤的）- 只在主页显示，不在详细面板中显示
                style.removeStyleLayer("raw-line-layer")
                style.removeStyleSource("raw-line-source")

                if (showRawTrack && rawTrackPoints.isNotEmpty() && !showDetailPanel) {
                    style.addSource(geoJsonSource("raw-line-source") {
                        feature(Feature.fromGeometry(LineString.fromLngLats(rawTrackPoints)))
                    })
                    style.addLayer(
                        lineLayer("raw-line-layer", "raw-line-source") {
                            lineColor("#FFA500") // 橙色表示原始轨迹
                            lineWidth((mapDrawLineWidth + 1).toDouble().coerceAtLeast(2.0)) // 比过滤后的轨迹稍粗一些
                            lineOpacity(mapDrawLineAlpha * 0.6) // 稍微透明一些
                            lineDasharray(listOf(3.0, 3.0)) // 虚线样式，更明显的间隔
                        }
                    )
                }
            }
        }

        // 一生模式数据点渲染
        MapEffect(lifetimeDataPoints, lifetimeDataVersion, selectedFilter) { mapView ->
            if (selectedFilter == 0) {
                mapView.getMapboxMap().getStyle { style ->
                    if (lifetimeDataPoints.isNotEmpty()) {
                        println("lifetime: 本次加载完成 ------ ${lifetimeDataPoints.size} data points (version: $lifetimeDataVersion) \n")

                        // 创建包含所有数据点的GeoJSON
                        val features = lifetimeDataPoints.map { point ->
                            Feature.fromGeometry(point)
                        }

                        // 检查source是否已存在
                        val existingSource = style.getSource("lifetime-points-source")
                        if (existingSource == null) {
                            println("lifetime: Creating new map source and layer for ${features.size} points")

                            // source不存在，创建新的
                            style.addSource(geoJsonSource("lifetime-points-source") {
                                features.forEach { feature(it) }
                            })

                            // 添加数据点图层 - 简单的小圆点
                            style.addLayer(
                                circleLayer("lifetime-points-layer", "lifetime-points-source") {
                                    circleRadius(1.5) // 小圆点
                                    circleColor(hexStringToMapColor(mapDrawLineColor)) // 使用配置的轨迹颜色
                                    circleOpacity(mapDrawSpotAlpha) // 使用配置的透明度
                                }
                            )
                        } else {
                            println("lifetime: 本次加载完成 ----- ${features.size} points \n")

                            // source已存在，更新数据
                            if (existingSource is GeoJsonSource) {
                                existingSource.featureCollection(
                                    FeatureCollection.fromFeatures(features)
                                )
                            }
                        }

//                        // 只在初次加载时调整地图视角
//                        if (lifetimeDataVersion == 1 && lifetimeDataPoints.size > 1) {
//                            fitTrackAndAnimate(mapView, mapViewportState, lifetimeDataPoints)
//                        }
                    }
                }
            } else {
                // 非一生模式时清除一生数据点图层
                println("lifetime: Clearing lifetime data layers (not in lifetime mode)")
                mapView.getMapboxMap().getStyle { style ->
                    style.removeStyleLayer("lifetime-points-layer")
                    style.removeStyleSource("lifetime-points-source")
                }
            }
        }

        // 一生模式地图视口变化监听
        MapEffect(selectedFilter) { mapView ->
            if (selectedFilter == 0) {
                println("lifetime: Setting up camera change listener for lifetime mode")

                mapView.getMapboxMap().addOnCameraChangeListener { cameraChangedEventData ->
                    // 获取当前地图边界
                    val bounds = mapView.getMapboxMap().coordinateBoundsForCamera(
                        mapView.getMapboxMap().cameraState.toCameraOptions()
                    )

                    // 获取当前缩放级别
                    val zoomLevel = mapView.getMapboxMap().cameraState.zoom

                    // 获取屏幕尺寸
                    val screenWidth = mapView.width
                    val screenHeight = mapView.height

                    if (screenWidth > 0 && screenHeight > 0) {
                        // 通知视口变化
                        onViewportChanged(
                            bounds.southwest.latitude(),
                            bounds.northeast.latitude(),
                            bounds.southwest.longitude(),
                            bounds.northeast.longitude(),
                            screenWidth,
                            screenHeight,
                            zoomLevel
                        )
                    }
                }
            } else {
                println("lifetime: Removing camera change listener (not in lifetime mode)")
            }
        }

        // 详细面板轨迹和选中点处理
        MapEffect(detailPanelTrackPoints, detailTrackDrawVersion, selectedDataPoint, selectedDataPointInfo) { mapView ->
            mapView.getMapboxMap().getStyle { style ->
                // 清除详细面板相关的图层和源
                style.removeStyleLayer("detail-line-layer")
                style.removeStyleSource("detail-line-source")
                style.removeStyleLayer("selected-point-layer")
                style.removeStyleSource("selected-point-source")
                style.removeStyleLayer("selected-point-text-layer")
                style.removeStyleSource("selected-point-text-source")

                // 绘制详细面板的轨迹
                if (detailPanelTrackPoints.isNotEmpty()) {
                    style.addSource(geoJsonSource("detail-line-source") {
                        feature(Feature.fromGeometry(LineString.fromLngLats(detailPanelTrackPoints)))
                    })
                    style.addLayer(
                        lineLayer("detail-line-layer", "detail-line-source") {
                            lineColor("#D92D34") // 蓝色
                            lineWidth(4.0)
                        }
                    )

                    // 自动调整视角到详细轨迹
                    val additionalPaddingDp = 200
                    val dynamicBottomPaddingDp = (detailPanelHeight + additionalPaddingDp).toDouble()
                    fitTrackAndAnimateWithPadding(
                        mapView = mapView,
                        mapViewportState = mapViewportState,
                        trackPoints = detailPanelTrackPoints,
                        bottomPaddingDp = dynamicBottomPaddingDp
                    )
                }

                // 绘制选中的数据点
                selectedDataPoint?.let { point ->
                    style.addSource(geoJsonSource("selected-point-source") {
                        feature(Feature.fromGeometry(point))
                    })

                    style.addLayer(
                        circleLayer("selected-point-layer", "selected-point-source") {
                            circleRadius(2.0)
                            circleColor("#FFFF00") // 黄色
                            circleStrokeColor("#FF0000") // 红色
                            circleStrokeWidth(2.0)
                        }
                    )

                    // 添加文字标注层
                    style.addSource(geoJsonSource("selected-point-text-source") {
                        feature(Feature.fromGeometry(point))
                    })

                    // 格式化时间显示
                    val timeText = selectedDataPointInfo?.let { dataInfo ->
                        val timeFormatter = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
                        timeFormatter.format(Date(dataInfo.dataTime * 1000))
                    } ?: "选中的点"

                    style.addLayer(
                        symbolLayer("selected-point-text-layer", "selected-point-text-source") {
                            textField(timeText)
                            textSize(12.0)
                            textColor("#FF0000") // 红色
                            textOffset(listOf(0.0, -1.0))
                            textAnchor(literal("bottom"))
                        }
                    )

                    // 将地图中心移动到选中的点
                    mapView.getMapboxMap().easeTo(
                        CameraOptions.Builder()
                            .center(point)
                            .zoom(16.0)
                            .padding(EdgeInsets(0.0, 0.0, screenHeight / 3, 0.0))
                            .build(),
                        MapAnimationOptions.Builder().duration(1000).build()
                    )
                }
            }
        }

        // 地图移动监听
        MapEffect(Unit) { mapView ->
            mapView.getMapboxMap().addOnMoveListener(object : OnMoveListener {
                override fun onMoveBegin(detector: MoveGestureDetector) {}

                override fun onMove(detector: MoveGestureDetector): Boolean {
                    return false
                }

                override fun onMoveEnd(detector: MoveGestureDetector) {
                    onDrawPointAnimationTypeChanged(DrawPointAnimationType.FREE_MODE)
                }
            })
        }
    }
}

/**
 * 根据配置参数获取地图样式 URL
 */
fun getMapStyleUrl(mapDisplayType: Int = 0, mapShowAddressName: Boolean = false): String {
    val styleType = mapDisplayType
    val hideMapLabel = !mapShowAddressName

    return when (styleType) {
        1 -> { // 清新蓝
            if (hideMapLabel) {
                "mapbox://styles/tobenum/ckgyxcd9f3oqd19pdn9kytnpl"
            } else {
                "mapbox://styles/tobenum/ck96rbsqm6c5t1il8yxfswl5u"
            }
        }
        2 -> { // 象牙白
            if (hideMapLabel) {
                "mapbox://styles/tobenum/ckgyx7v2o6s271an2i77aqyfv"
            } else {
                "mapbox://styles/tobenum/ck98ku7zo3yje1inx4druoygg"
            }
        }
        3 -> { // 薄荷绿
            if (hideMapLabel) {
                "mapbox://styles/tobenum/ckgywzqvm4erm19ngaqbl0fjz"
            } else {
                "mapbox://styles/tobenum/ckawjeygt6of21iqpeg4nwgpo"
            }
        }
        4 -> { // 樱桃粉
            if (hideMapLabel) {
                "mapbox://styles/tobenum/ckgyy8tpd105419rve42dngjo"
            } else {
                "mapbox://styles/tobenum/ckawk0vsc30js1iphqficjwvy"
            }
        }
        5 -> { // 卫星图
            if (hideMapLabel) {
                "mapbox://styles/tobenum/clyh1i5ja00vg01qp8xm3cueu"
            } else {
                "mapbox://styles/tobenum/clyh0mos600wf01pmcdchawrc"
            }
        }
        6 -> { // 户外地图
            if (hideMapLabel) {
                "mapbox://styles/tobenum/cmcev0ak001yy01r75zrgdfy1"
            } else {
                "mapbox://styles/tobenum/clyh1u70o00vu01pn0ruq0ggr"
            }
        }
        else -> { // 通用地图 (默认)
            if (hideMapLabel) {
                "mapbox://styles/tobenum/ckgyw4ne64dvr19p96wtr692g"
            } else {
                "mapbox://styles/tobenum/cjsnbd8cy27ki1flo6jdetski"
            }
        }
    }
}

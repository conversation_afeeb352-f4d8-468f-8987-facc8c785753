package com.lfb.android.footprint.location

import android.location.Location
import com.lfb.android.footprint.prefs.AppPrefs
import kotlin.math.*

/**
 * GPS偏移过滤器
 * 用于检测和过滤GPS轨迹中的偏移数据点（偏移出去又回来的异常轨迹）
 *
 * 算法原理：
 * 1. 维护一个历史位置缓冲区
 * 2. 对于新的位置点，检查是否偏离了主要轨迹路径
 * 3. 如果偏离，则暂时缓存该点，等待后续点验证
 * 4. 如果后续点回到主轨迹附近，则认为是偏移数据，过滤掉
 * 5. 如果后续点继续偏离，则认为是正常轨迹变化，接受所有缓存点
 */
class GpsOffsetFilter {
    
    companion object {
        private const val TAG = "GpsOffsetFilter"
        
        // 历史位置缓冲区大小
        private const val HISTORY_BUFFER_SIZE = 5
        
        // 可疑位置缓冲区大小
        private const val SUSPICIOUS_BUFFER_SIZE = 3
        
        // 偏移检测的最小距离阈值（米）
        private const val MIN_OFFSET_DISTANCE = 50.0
        
        // 偏移检测的最大距离阈值（米）
        private const val MAX_OFFSET_DISTANCE = 500.0
        
        // 回归检测的距离阈值（米）
        private const val RETURN_DISTANCE_THRESHOLD = 80.0
        
        // 精度阈值，低于此精度的点更容易被认为是偏移
        private const val LOW_ACCURACY_THRESHOLD = 50.0
    }
    
    // 历史位置缓冲区（用于计算主轨迹方向）
    private val historyBuffer = mutableListOf<Location>()
    
    // 可疑位置缓冲区（暂时缓存可能的偏移点）
    private val suspiciousBuffer = mutableListOf<Location>()
    
    // 上一个被接受的位置
    private var lastAcceptedLocation: Location? = null
    
    /**
     * 判断是否应该接受这个位置点
     * @param location 新的位置点
     * @return true表示接受，false表示过滤掉
     */
    fun shouldAcceptLocation(location: Location): Boolean {
        // 检查是否启用过滤器
        if (!AppPrefs.sharedInstance.gpsOffsetFilterEnabled) {
            return true
        }
        // 第一个点总是接受
        if (historyBuffer.isEmpty()) {
            addToHistory(location)
            lastAcceptedLocation = location
            return true
        }
        

        
        // 如果历史数据不足，直接接受
        if (historyBuffer.size < 3) {
            addToHistory(location)
            lastAcceptedLocation = location
            return true
        }
        
        // 检查是否为偏移点
        val isOffset = isOffsetLocation(location)
        
        if (isOffset) {
            // 添加到可疑缓冲区
            suspiciousBuffer.add(location)
            
            // 如果可疑缓冲区满了，需要做决策
            if (suspiciousBuffer.size >= SUSPICIOUS_BUFFER_SIZE) {
                return handleSuspiciousBuffer()
            }
            
            // 暂时不接受，等待更多数据
            return false
        } else {
            // 不是偏移点
            if (suspiciousBuffer.isNotEmpty()) {
                // 检查是否回归到主轨迹
                val isReturning = isReturningToMainTrack(location)
                
                if (isReturning) {
                    // 确认是偏移数据，清空可疑缓冲区，接受当前点
                    suspiciousBuffer.clear()
                    addToHistory(location)
                    lastAcceptedLocation = location
                    return true
                } else {
                    // 不是回归，说明轨迹确实在变化，接受所有缓存的点
                    for (suspiciousLocation in suspiciousBuffer) {
                        addToHistory(suspiciousLocation)
                    }
                    suspiciousBuffer.clear()
                    addToHistory(location)
                    lastAcceptedLocation = location
                    return true
                }
            } else {
                // 正常点，直接接受
                addToHistory(location)
                lastAcceptedLocation = location
                return true
            }
        }
    }
    
    /**
     * 检查位置是否为偏移点
     */
    private fun isOffsetLocation(location: Location): Boolean {
        if (historyBuffer.size < 3) return false

        val recentLocations = historyBuffer.takeLast(3)
        val avgLat = recentLocations.map { it.latitude }.average()
        val avgLng = recentLocations.map { it.longitude }.average()

        // 创建平均位置点
        val avgLocation = Location("").apply {
            latitude = avgLat
            longitude = avgLng
        }

        val distanceFromAvg = location.distanceTo(avgLocation)

        // 基础距离检查
        if (distanceFromAvg < MIN_OFFSET_DISTANCE) {
            return false
        }

        // 检查时间间隔因子
        val timeIntervalFactor = checkTimeIntervalFactor(location)

        // 如果时间间隔很长，降低偏移判断的敏感度
        if (distanceFromAvg > MAX_OFFSET_DISTANCE * timeIntervalFactor) {
            return true
        }

        // 精度检查：低精度的点更容易被认为是偏移
        val accuracyFactor = if (location.accuracy > LOW_ACCURACY_THRESHOLD) 1.5 else 1.0

        // 速度变化检查
        val speedChangeFactor = checkSpeedChange(location)

        // 方向一致性检查
        val directionFactor = checkDirectionConsistency(location)

        // 综合评分
        val offsetScore = distanceFromAvg * accuracyFactor * speedChangeFactor * directionFactor

        // 动态阈值，根据用户设置的敏感度和时间间隔调整
        val sensitivity = AppPrefs.sharedInstance.gpsOffsetFilterSensitivity
        val baseThreshold = MIN_OFFSET_DISTANCE + (MAX_OFFSET_DISTANCE - MIN_OFFSET_DISTANCE) * 0.6
        val threshold = (baseThreshold / sensitivity) * timeIntervalFactor // 时间间隔长时提高阈值

        return offsetScore > threshold
    }
    
    /**
     * 检查时间间隔因子
     * 时间间隔越长，越可能是正常的位置变化，应该降低偏移判断的敏感度
     */
    private fun checkTimeIntervalFactor(location: Location): Double {
        val lastLocation = historyBuffer.lastOrNull() ?: return 1.0

        val timeInterval = abs(location.time - lastLocation.time) / 1000.0 // 转换为秒

        return when {
            timeInterval < 60 -> 1.0      // 1分钟内，正常敏感度
            timeInterval < 300 -> 1.2     // 5分钟内，稍微降低敏感度
            timeInterval < 1800 -> 1.5    // 30分钟内，明显降低敏感度
            else -> 2.0                   // 超过30分钟，大幅降低敏感度
        }
    }

    /**
     * 检查速度变化因子
     * 基于假设：GPS偏移点往往伴随着不合理的速度变化
     * 比如：步行时突然显示高速，或者高速行驶时突然显示静止
     */
    private fun checkSpeedChange(location: Location): Double {
        val lastLocation = historyBuffer.lastOrNull() ?: return 1.0

        val currentSpeed = location.speed
        val lastSpeed = lastLocation.speed
        val speedDiff = abs(currentSpeed - lastSpeed)

        // 如果速度数据无效（负值或异常大），不使用速度因子
        if (currentSpeed < 0 || lastSpeed < 0 || currentSpeed > 100 || lastSpeed > 100) {
            return 1.0
        }

        // 计算时间间隔
        val timeInterval = abs(location.time - lastLocation.time) / 1000.0 // 秒
        if (timeInterval <= 0) return 1.0

        // 计算理论最大加速度变化（考虑GPS精度误差）
        val maxReasonableAcceleration = when {
            max(currentSpeed, lastSpeed) < 2.0 -> 5.0  // 步行/慢速：最大5m/s²
            max(currentSpeed, lastSpeed) < 15.0 -> 8.0 // 骑车/中速：最大8m/s²
            else -> 12.0 // 开车/高速：最大12m/s²
        }

        val maxSpeedChange = maxReasonableAcceleration * timeInterval

        return when {
            speedDiff <= maxSpeedChange -> 1.0 // 合理的速度变化
            speedDiff <= maxSpeedChange * 2 -> 1.2 // 稍微不合理
            speedDiff <= maxSpeedChange * 4 -> 1.4 // 明显不合理
            else -> 1.6 // 严重不合理，很可能是偏移
        }
    }
    
    /**
     * 检查方向一致性因子
     */
    private fun checkDirectionConsistency(location: Location): Double {
        if (historyBuffer.size < 2) return 1.0
        
        val recent = historyBuffer.takeLast(2)
        val prevLocation = recent[0]
        val currentLocation = recent[1]
        
        // 计算历史方向
        val historyBearing = prevLocation.bearingTo(currentLocation)
        
        // 计算新方向
        val newBearing = currentLocation.bearingTo(location)
        
        // 计算角度差
        var angleDiff = abs(historyBearing - newBearing)
        if (angleDiff > 180) {
            angleDiff = 360 - angleDiff
        }
        
        // 如果方向变化超过90度，增加偏移可能性
        return if (angleDiff > 90) 1.4 else 1.0
    }
    
    /**
     * 检查是否回归到主轨迹
     */
    private fun isReturningToMainTrack(location: Location): Boolean {
        val lastAccepted = lastAcceptedLocation ?: return false
        
        val distanceToLastAccepted = location.distanceTo(lastAccepted)
        
        // 如果距离上一个接受的点很近，认为是回归
        return distanceToLastAccepted <= RETURN_DISTANCE_THRESHOLD
    }
    
    /**
     * 处理可疑缓冲区满的情况
     */
    private fun handleSuspiciousBuffer(): Boolean {
        // 如果可疑缓冲区满了，说明可能是真实的轨迹变化
        // 接受所有缓存的点
        for (suspiciousLocation in suspiciousBuffer) {
            addToHistory(suspiciousLocation)
        }
        suspiciousBuffer.clear()
        return true
    }
    
    /**
     * 添加位置到历史缓冲区
     */
    private fun addToHistory(location: Location) {
        historyBuffer.add(location)
        if (historyBuffer.size > HISTORY_BUFFER_SIZE) {
            historyBuffer.removeAt(0)
        }
    }
    
    /**
     * 重置过滤器状态
     */
    private fun resetFilter() {
        historyBuffer.clear()
        suspiciousBuffer.clear()
        lastAcceptedLocation = null
    }
    
    /**
     * 获取当前过滤器状态信息（用于调试）
     */
    fun getFilterStatus(): String {
        return "History: ${historyBuffer.size}, Suspicious: ${suspiciousBuffer.size}"
    }
}

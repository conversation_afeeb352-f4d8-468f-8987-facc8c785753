package com.lfb.android.footprint.location

import android.location.Location
import com.lfb.android.footprint.prefs.AppPrefs
import kotlin.math.*

/**
 * GPS偏移过滤器
 * 用于检测和过滤GPS轨迹中的偏移数据点（偏移出去又回来的异常轨迹）
 *
 * 核心算法原理：
 * 检测"突兀"的轨迹点 - 即相对于前后轨迹都显得不连续的点
 * 1. 维护一个位置缓冲区，等待足够的前后轨迹数据
 * 2. 当有足够数据时，检查中间的点是否相对于前后轨迹都偏离较远
 * 3. 如果一个点到前面轨迹和后面轨迹的距离都很大，则认为是偏移点
 * 4. 使用延迟判断机制，确保有足够的后续数据来验证
 */
class GpsOffsetFilter {
    
    companion object {
        private const val TAG = "GpsOffsetFilter"

        // 轨迹缓冲区大小 - 需要足够的前后数据来判断突兀性
        private const val TRACK_BUFFER_SIZE = 7

        // 延迟判断的缓冲区大小 - 等待后续数据
        private const val PENDING_BUFFER_SIZE = 3

        // 突兀点检测的角度阈值（度）- 前后轨迹方向差异超过此角度认为突兀
        private const val OUTLIER_ANGLE_THRESHOLD = 120.0

        // 最小距离阈值（米）- 距离太近时角度计算不准确
        private const val MIN_DISTANCE_FOR_ANGLE = 20.0

        // GPS精度阈值，低于此精度的点更容易被认为是偏移
        private const val LOW_ACCURACY_THRESHOLD = 50.0
    }
    
    // 轨迹缓冲区（存储所有接收到的位置点，用于前后轨迹分析）
    private val trackBuffer = mutableListOf<Location>()

    // 待处理的位置缓冲区（等待后续数据来判断的点）
    private val pendingBuffer = mutableListOf<Location>()

    // 已确认接受的位置点列表
    private val acceptedLocations = mutableListOf<Location>()
    
    /**
     * 判断是否应该接受这个位置点
     * @param location 新的位置点
     * @return true表示接受，false表示过滤掉
     */
    fun shouldAcceptLocation(location: Location): Boolean {
        // 检查是否启用过滤器
        if (!AppPrefs.sharedInstance.gpsOffsetFilterEnabled) {
            return true
        }

        // 将新位置添加到轨迹缓冲区
        addToTrackBuffer(location)

        // 前几个点直接接受，因为没有足够的前后数据来判断
        if (trackBuffer.size <= 3) {
            acceptedLocations.add(location)
            return true
        }

        // 处理待处理缓冲区中的点
        processPendingLocations()

        // 将当前点添加到待处理缓冲区
        pendingBuffer.add(location)

        // 如果待处理缓冲区满了，强制处理最早的点
        if (pendingBuffer.size > PENDING_BUFFER_SIZE) {
            val locationToProcess = pendingBuffer.removeAt(0)
            val shouldAccept = !isOutlierLocation(locationToProcess)
            if (shouldAccept) {
                acceptedLocations.add(locationToProcess)
            }
            return shouldAccept
        }

        // 暂时不返回结果，等待更多数据
        return true // 临时返回true，实际的过滤在processPendingLocations中进行
    }
    
    /**
     * 处理待处理缓冲区中的位置点
     */
    private fun processPendingLocations() {
        // 如果轨迹数据不足，无法判断
        if (trackBuffer.size < 5) return

        val locationsToProcess = mutableListOf<Location>()

        // 检查待处理缓冲区中的每个点
        for (i in pendingBuffer.indices) {
            val location = pendingBuffer[i]
            if (canProcessLocation(location)) {
                locationsToProcess.add(location)
            }
        }

        // 处理可以判断的位置点
        for (location in locationsToProcess) {
            pendingBuffer.remove(location)
            val shouldAccept = !isOutlierLocation(location)
            if (shouldAccept) {
                acceptedLocations.add(location)
            }
        }
    }

    /**
     * 检查是否可以处理某个位置点（是否有足够的前后数据）
     */
    private fun canProcessLocation(location: Location): Boolean {
        val locationIndex = trackBuffer.indexOf(location)
        if (locationIndex == -1) return false

        // 需要前面至少有2个点，后面至少有2个点
        return locationIndex >= 2 && locationIndex < trackBuffer.size - 2
    }

    /**
     * 检查位置是否为突兀的偏移点
     * 核心逻辑：检查该点是否破坏了轨迹的自然连续性
     *
     * 关键思路：
     * - 真正的偏移：该点偏离了前后两点的合理连接路径
     * - 正常转弯/调头：虽然角度变化大，但轨迹仍然连续合理
     * - 判断标准：点到直线的偏离距离 + 路径合理性
     */
    private fun isOutlierLocation(location: Location): Boolean {
        val locationIndex = trackBuffer.indexOf(location)
        if (locationIndex == -1 || locationIndex < 1 || locationIndex >= trackBuffer.size - 1) {
            return false
        }

        val beforePoint = trackBuffer[locationIndex - 1]
        val afterPoint = trackBuffer[locationIndex + 1]

        // 检查距离是否足够进行分析
        val distanceBefore = beforePoint.distanceTo(location)
        val distanceAfter = location.distanceTo(afterPoint)
        val directDistance = beforePoint.distanceTo(afterPoint)

        if (distanceBefore < MIN_DISTANCE_FOR_ANGLE ||
            distanceAfter < MIN_DISTANCE_FOR_ANGLE ||
            directDistance < MIN_DISTANCE_FOR_ANGLE) {
            return false // 距离太近，无法准确判断
        }

        // 核心判断：检查该点是否偏离了前后两点的合理路径
        return checkTrajectoryDeviation(beforePoint, location, afterPoint)
    }

    /**
     * 检查轨迹偏离度
     * 判断中间点是否偏离了前后两点的合理连接路径
     *
     * 这种方法可以区分：
     * - 偏移：点偏离直线很远，且绕行距离不合理
     * - 转弯：虽然不在直线上，但绕行距离合理
     * - 调头：绕行距离较长，但仍在合理范围内
     */
    private fun checkTrajectoryDeviation(beforePoint: Location, middlePoint: Location, afterPoint: Location): Boolean {
        // 计算点到直线的距离（中间点到前后两点连线的距离）
        val deviationDistance = calculatePointToLineDistance(beforePoint, afterPoint, middlePoint)

        // 计算路径长度比 = (前→中→后的距离) / (前→后的直线距离)
        val actualPath = beforePoint.distanceTo(middlePoint) + middlePoint.distanceTo(afterPoint)
        val directPath = beforePoint.distanceTo(afterPoint)
        val pathRatio = if (directPath > 0) actualPath / directPath else 1.0

        // 获取用户设置的敏感度
        val sensitivity = AppPrefs.sharedInstance.gpsOffsetFilterSensitivity

        // 偏离距离阈值（米）- 考虑GPS精度
        val baseDeviationThreshold = 80.0
        val accuracyFactor = if (middlePoint.accuracy > LOW_ACCURACY_THRESHOLD) 1.5 else 1.0
        val deviationThreshold = (baseDeviationThreshold * accuracyFactor) / sensitivity

        // 路径比例阈值 - 正常转弯/调头的绕行比例
        // 即使是180度调头，路径比例通常也不会超过2.5
        val pathRatioThreshold = 3.0 / sensitivity

        // 同时满足两个条件才认为是偏移：
        // 1. 偏离直线距离过大
        // 2. 绕行距离过长（超出正常转弯/调头范围）
        return deviationDistance > deviationThreshold && pathRatio.toDouble() > pathRatioThreshold
    }
    
    /**
     * 计算点到直线的距离
     * 使用向量叉积计算点到直线的垂直距离
     */
    private fun calculatePointToLineDistance(lineStart: Location, lineEnd: Location, point: Location): Double {
        // 将经纬度转换为平面坐标（简化计算，适用于小范围）
        val x1 = lineStart.longitude
        val y1 = lineStart.latitude
        val x2 = lineEnd.longitude
        val y2 = lineEnd.latitude
        val x0 = point.longitude
        val y0 = point.latitude

        // 计算直线的长度
        val lineLength = sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1))

        if (lineLength == 0.0) {
            // 如果直线长度为0，返回点到点的距离
            return lineStart.distanceTo(point).toDouble()
        }

        // 使用点到直线距离公式：|ax0 + by0 + c| / sqrt(a² + b²)
        // 直线方程：(y2-y1)x - (x2-x1)y + (x2-x1)y1 - (y2-y1)x1 = 0
        val a = y2 - y1
        val b = x1 - x2
        val c = (x2 - x1) * y1 - (y2 - y1) * x1

        val distance = abs(a * x0 + b * y0 + c) / sqrt(a * a + b * b)

        // 将度数差转换为大概的米数（1度约等于111000米）
        return distance * 111000.0
    }

    /**
     * 计算两个角度之间的差异（0-180度）
     */
    private fun calculateAngleDifference(angle1: Float, angle2: Float): Double {
        var diff = abs(angle1 - angle2).toDouble()
        if (diff > 180) {
            diff = 360 - diff
        }
        return diff
    }


    
    /**
     * 添加位置到轨迹缓冲区
     */
    private fun addToTrackBuffer(location: Location) {
        trackBuffer.add(location)
        if (trackBuffer.size > TRACK_BUFFER_SIZE) {
            trackBuffer.removeAt(0)
        }
    }

    /**
     * 重置过滤器状态
     */
    private fun resetFilter() {
        trackBuffer.clear()
        pendingBuffer.clear()
        acceptedLocations.clear()
    }

    /**
     * 获取当前过滤器状态信息（用于调试）
     */
    fun getFilterStatus(): String {
        return "Track: ${trackBuffer.size}, Pending: ${pendingBuffer.size}, Accepted: ${acceptedLocations.size}"
    }

    /**
     * 强制处理所有待处理的位置点（在停止定位时调用）
     */
    fun flushPendingLocations(): List<Location> {
        val result = mutableListOf<Location>()

        // 处理所有待处理的点
        for (location in pendingBuffer) {
            val shouldAccept = !isOutlierLocation(location)
            if (shouldAccept) {
                acceptedLocations.add(location)
                result.add(location)
            }
        }

        pendingBuffer.clear()
        return result
    }
}

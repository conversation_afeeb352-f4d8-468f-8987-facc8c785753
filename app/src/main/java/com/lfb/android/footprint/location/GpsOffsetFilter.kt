package com.lfb.android.footprint.location

import android.location.Location
import com.lfb.android.footprint.prefs.AppPrefs
import kotlin.math.*

/**
 * GPS偏移过滤器
 * 用于检测和过滤GPS轨迹中的偏移数据点（偏移出去又回来的异常轨迹）
 *
 * 核心算法原理：
 * 检测"突兀"的轨迹点 - 即相对于前后轨迹都显得不连续的点
 * 1. 维护一个位置缓冲区，等待足够的前后轨迹数据
 * 2. 当有足够数据时，检查中间的点是否相对于前后轨迹都偏离较远
 * 3. 如果一个点到前面轨迹和后面轨迹的距离都很大，则认为是偏移点
 * 4. 使用延迟判断机制，确保有足够的后续数据来验证
 */
class GpsOffsetFilter {
    
    companion object {
        private const val TAG = "GpsOffsetFilter"

        // 轨迹缓冲区大小 - 需要足够的前后数据来判断突兀性
        private const val TRACK_BUFFER_SIZE = 7

        // 延迟判断的缓冲区大小 - 等待后续数据
        private const val PENDING_BUFFER_SIZE = 3

        // 突兀点检测的距离阈值（米）
        private const val OUTLIER_DISTANCE_THRESHOLD = 100.0

        // GPS精度阈值，低于此精度的点更容易被认为是偏移
        private const val LOW_ACCURACY_THRESHOLD = 50.0
    }
    
    // 轨迹缓冲区（存储所有接收到的位置点，用于前后轨迹分析）
    private val trackBuffer = mutableListOf<Location>()

    // 待处理的位置缓冲区（等待后续数据来判断的点）
    private val pendingBuffer = mutableListOf<Location>()

    // 已确认接受的位置点列表
    private val acceptedLocations = mutableListOf<Location>()
    
    /**
     * 判断是否应该接受这个位置点
     * @param location 新的位置点
     * @return true表示接受，false表示过滤掉
     */
    fun shouldAcceptLocation(location: Location): Boolean {
        // 检查是否启用过滤器
        if (!AppPrefs.sharedInstance.gpsOffsetFilterEnabled) {
            return true
        }

        // 将新位置添加到轨迹缓冲区
        addToTrackBuffer(location)

        // 前几个点直接接受，因为没有足够的前后数据来判断
        if (trackBuffer.size <= 3) {
            acceptedLocations.add(location)
            return true
        }

        // 处理待处理缓冲区中的点
        processPendingLocations()

        // 将当前点添加到待处理缓冲区
        pendingBuffer.add(location)

        // 如果待处理缓冲区满了，强制处理最早的点
        if (pendingBuffer.size > PENDING_BUFFER_SIZE) {
            val locationToProcess = pendingBuffer.removeAt(0)
            val shouldAccept = !isOutlierLocation(locationToProcess)
            if (shouldAccept) {
                acceptedLocations.add(locationToProcess)
            }
            return shouldAccept
        }

        // 暂时不返回结果，等待更多数据
        return true // 临时返回true，实际的过滤在processPendingLocations中进行
    }
    
    /**
     * 处理待处理缓冲区中的位置点
     */
    private fun processPendingLocations() {
        // 如果轨迹数据不足，无法判断
        if (trackBuffer.size < 5) return

        val locationsToProcess = mutableListOf<Location>()

        // 检查待处理缓冲区中的每个点
        for (i in pendingBuffer.indices) {
            val location = pendingBuffer[i]
            if (canProcessLocation(location)) {
                locationsToProcess.add(location)
            }
        }

        // 处理可以判断的位置点
        for (location in locationsToProcess) {
            pendingBuffer.remove(location)
            val shouldAccept = !isOutlierLocation(location)
            if (shouldAccept) {
                acceptedLocations.add(location)
            }
        }
    }

    /**
     * 检查是否可以处理某个位置点（是否有足够的前后数据）
     */
    private fun canProcessLocation(location: Location): Boolean {
        val locationIndex = trackBuffer.indexOf(location)
        if (locationIndex == -1) return false

        // 需要前面至少有2个点，后面至少有2个点
        return locationIndex >= 2 && locationIndex < trackBuffer.size - 2
    }

    /**
     * 检查位置是否为突兀的偏移点
     * 核心逻辑：检查该点是否相对于前后轨迹都显得突兀
     */
    private fun isOutlierLocation(location: Location): Boolean {
        val locationIndex = trackBuffer.indexOf(location)
        if (locationIndex == -1 || locationIndex < 2 || locationIndex >= trackBuffer.size - 2) {
            return false
        }

        // 获取前面的轨迹点（2个点）
        val beforePoints = trackBuffer.subList(locationIndex - 2, locationIndex)

        // 获取后面的轨迹点（2个点）
        val afterPoints = trackBuffer.subList(locationIndex + 1, locationIndex + 3)

        // 计算该点到前面轨迹的距离
        val distanceToBefore = calculateDistanceToTrackSegment(location, beforePoints)

        // 计算该点到后面轨迹的距离
        val distanceToAfter = calculateDistanceToTrackSegment(location, afterPoints)

        // 获取用户设置的敏感度
        val sensitivity = AppPrefs.sharedInstance.gpsOffsetFilterSensitivity
        val threshold = OUTLIER_DISTANCE_THRESHOLD / sensitivity

        // 考虑GPS精度因子
        val accuracyFactor = if (location.accuracy > LOW_ACCURACY_THRESHOLD) 1.5 else 1.0
        val adjustedThreshold = threshold * accuracyFactor

        // 如果该点到前后轨迹的距离都超过阈值，则认为是突兀点
        return distanceToBefore > adjustedThreshold && distanceToAfter > adjustedThreshold
    }
    
    /**
     * 计算点到轨迹段的距离
     * @param point 要检查的点
     * @param trackPoints 轨迹段的点列表
     * @return 点到轨迹段的最小距离
     */
    private fun calculateDistanceToTrackSegment(point: Location, trackPoints: List<Location>): Double {
        if (trackPoints.isEmpty()) return Double.MAX_VALUE

        if (trackPoints.size == 1) {
            return point.distanceTo(trackPoints[0]).toDouble()
        }

        // 计算点到轨迹段中心的距离
        val centerLat = trackPoints.map { it.latitude }.average()
        val centerLng = trackPoints.map { it.longitude }.average()

        val centerLocation = Location("").apply {
            latitude = centerLat
            longitude = centerLng
        }

        return point.distanceTo(centerLocation).toDouble()
    }


    
    /**
     * 添加位置到轨迹缓冲区
     */
    private fun addToTrackBuffer(location: Location) {
        trackBuffer.add(location)
        if (trackBuffer.size > TRACK_BUFFER_SIZE) {
            trackBuffer.removeAt(0)
        }
    }

    /**
     * 重置过滤器状态
     */
    private fun resetFilter() {
        trackBuffer.clear()
        pendingBuffer.clear()
        acceptedLocations.clear()
    }

    /**
     * 获取当前过滤器状态信息（用于调试）
     */
    fun getFilterStatus(): String {
        return "Track: ${trackBuffer.size}, Pending: ${pendingBuffer.size}, Accepted: ${acceptedLocations.size}"
    }

    /**
     * 强制处理所有待处理的位置点（在停止定位时调用）
     */
    fun flushPendingLocations(): List<Location> {
        val result = mutableListOf<Location>()

        // 处理所有待处理的点
        for (location in pendingBuffer) {
            val shouldAccept = !isOutlierLocation(location)
            if (shouldAccept) {
                acceptedLocations.add(location)
                result.add(location)
            }
        }

        pendingBuffer.clear()
        return result
    }
}
